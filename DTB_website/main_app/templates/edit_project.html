{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="form-container">
  <h1>Edit Project</h1>
  
  <form method="POST" enctype="multipart/form-data">
    {% csrf_token %}
    
    <div class="form-group">
      <label for="{{ form.title.id_for_label }}">Title</label>
      {{ form.title }}
    </div>
    
    <div class="form-group">
      <label for="{{ form.short_description.id_for_label }}">Short Description</label>
      {{ form.short_description }}
    </div>
    
    <div class="form-group">
      <label for="{{ form.full_description.id_for_label }}">Full Description</label>
      {{ form.full_description }}
    </div>
    
    <div class="form-group">
      <label for="{{ form.project_url.id_for_label }}">Project URL</label>
      {{ form.project_url }}
    </div>
    
    <div class="form-group">
      <label for="{{ form.technologies.id_for_label }}">Technologies</label>
      {{ form.technologies }}
      <small>Separate technologies with commas</small>
    </div>
    
    <div class="form-group">
      <label for="{{ form.image.id_for_label }}">Project Image</label>
      {% if project.image %}
        <div class="current-image">
          <img src="{{ project.image.url }}" alt="{{ project.title }}" style="max-width: 200px; margin-bottom: 10px;">
          <p>Current image</p>
        </div>
      {% endif %}
      {{ form.image }}
    </div>
    
    <div class="form-actions">
      <button type="submit" class="btn submit">Update Project</button>
      <a href="{% url 'project_detail' project.id %}" class="btn cancel">Cancel</a>
    </div>
  </form>
</div>

<style>
  .form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }
  
  h1 {
    color: #f9f9fa;
    margin-bottom: 30px;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  label {
    display: block;
    margin-bottom: 5px;
    color: #f9f9fa;
    font-weight: bold;
  }
  
  input[type="text"],
  input[type="url"],
  textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #444;
    background-color: #333;
    color: #f9f9fa;
    border-radius: 4px;
  }
  
  small {
    display: block;
    margin-top: 5px;
    color: #999;
  }
  
  .form-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
  }
  
  .btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
    border: none;
    cursor: pointer;
  }
  
  .btn.submit {
    background-color: #d35208;
    color: white;
  }
  
  .btn.submit:hover {
    background-color: #9e3902;
  }
  
  .btn.cancel {
    background-color: #333;
    color: white;
  }
  
  .btn.cancel:hover {
    background-color: #444;
  }
  
  .current-image {
    margin-bottom: 10px;
  }
</style>
{% endblock %}