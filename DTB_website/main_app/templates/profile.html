{% extends 'base.html' %}
{% load static %}

{% block head %}
<style>
  .profile-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
  }
  
  .profile-header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    gap: 30px;
  }
  
  .profile-image-container {
    position: relative;
    width: 200px;
    height: 200px;
  }
  
  .profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid var(--nav-bg);
  }
  
  .profile-image-placeholder {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #ccc;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-l);
    color: #666;
  }
  
  .upload-icon {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: var(--nav-bg);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  
  .profile-info {
    flex: 1;
  }
  
  .profile-title {
    font-size: var(--font-xl);
    margin-bottom: 10px;
  }
  
  .profile-bio {
    margin-bottom: 20px;
  }
  
  .profile-section {
    margin-bottom: 30px;
  }
  
  .section-title {
    font-size: var(--font-l);
    margin-bottom: 15px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
  }
  
  .featured-project-selector {
    width: 100%;
    padding: 10px;
    margin-bottom: 20px;
    border-radius: var(--card-border-radius);
    border: 1px solid #ddd;
  }
  
  .image-carousel {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  
  .carousel-button {
    background-color: var(--nav-bg);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  
  .carousel-images {
    display: flex;
    overflow-x: hidden;
    scroll-behavior: smooth;
    width: 100%;
    padding: 10px 0;
    gap: 20px;
  }
  
  .carousel-item {
    min-width: 200px;
    text-align: center;
  }
  
  .carousel-image {
    width: 200px;
    height: 200px;
    object-fit: contain;
    border: 1px solid #ddd;
    border-radius: var(--card-border-radius);
    margin-bottom: 10px;
  }
  
  .carousel-label {
    font-weight: bold;
  }
  
  .incomplete-profile {
    background-color: #f8f8f8;
    border: 2px dashed #ccc;
    border-radius: var(--card-border-radius);
    padding: 20px;
    text-align: center;
    margin-bottom: 30px;
  }
  
  .incomplete-profile h3 {
    color: #666;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
  }
  
  .form-group input[type="text"],
  .form-group textarea {
    width: 100%;
    padding: 10px;
    border-radius: var(--card-border-radius);
    border: 1px solid #ddd;
  }
  
  .form-group textarea {
    min-height: 100px;
  }
  
  .hidden-file-input {
    display: none;
  }
  
  .certificate-container {
    width: 150px;
    height: 150px;
    border: 1px solid #ddd;
    border-radius: var(--card-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    overflow: hidden;
  }
  
  .certificate-image {
    max-width: 100%;
    max-height: 100%;
  }
  
  .certificate-placeholder {
    color: #666;
    font-size: var(--font-reg);
  }
  
  .project-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
  }
  
  .project-card {
    border: 1px solid #ddd;
    border-radius: var(--card-border-radius);
    padding: 15px;
  }
  
  .project-card h3 {
    margin-top: 0;
    font-size: var(--font-reg);
  }
  
  .project-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
  }

  
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
  <h1>Your Profile</h1>
  
  {% if not profile_complete %}
  <div class="incomplete-profile">
    <h3>Complete Your Profile</h3>
    <p>Your profile is incomplete. Please fill in all required information to activate your account and be visible on the site.</p>
  </div>
  {% endif %}
  
  <form method="post" enctype="multipart/form-data">
    {% csrf_token %}
    
    <div class="profile-header">
      <div class="profile-image-container">
        {% if profile.profile_picture %}
          <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" class="profile-image">
        {% else %}
          <div class="profile-image-placeholder">
            <span>Add Profile Picture</span>
          </div>
        {% endif %}
        <label for="profile-picture-upload" class="upload-icon">
          <span><img src="{% static 'Images/pfp_icon.png' %}" alt="pfp Icon" style="width: 65px; height: 40px;"></span>
        </label>
        <input type="file" id="profile-picture-upload" name="profile_picture" class="hidden-file-input" accept="image/*">
      </div>
      
      <div class="profile-info">
        <div class="form-group">
          <label for="dev-name">Your Name</label>
          <input type="text" id="dev-name" name="name" value="{{ profile.name|default:'' }}" required>
        </div>
        
        <div class="form-group">
          <label for="dev-title">Professional Title</label>
          <input type="text" id="dev-title" name="title" value="{{ profile.title|default:'' }}" required>
        </div>
      </div>
    </div>
    
    <div class="profile-section">
      <h2 class="section-title">Bio</h2>
      <div class="form-group">
        <textarea name="bio" placeholder="Tell us about yourself, your skills, and experience...">{{ profile.bio|default:'' }}</textarea>
      </div>
    </div>
    
    <div class="profile-section">
      <h2 class="section-title">Featured Project</h2>
      {% if projects %}
        <select name="featured_project" class="featured-project-selector">
          <option value="">-- Select a featured project --</option>
          {% for project in projects %}
            <option value="{{ project.id }}" {% if project.id == profile.featured_project.id %}selected{% endif %}>
              {{ project.title }}
            </option>
          {% endfor %}
        </select>
      {% else %}
        <p>You haven't added any projects yet. Add a project to select it as your featured work.</p>
      {% endif %}
    </div>
    
    <div class="profile-section">
      <h2 class="section-title">Certificate/Credentials</h2>
      <div class="certificate-container">
        {% if profile.certificate %}
          <img src="{{ profile.certificate.url }}" alt="Certificate" class="certificate-image">
        {% else %}
          <div class="certificate-placeholder">
            <span>Add Certificate</span>
          </div>
        {% endif %}
      </div>
      <label for="certificate-upload" class="btn submit">
        Upload Certificate
      </label>
      <input type="file" id="certificate-upload" name="certificate" class="hidden-file-input" accept="image/*">
    </div>
    
    <button type="submit" class="btn submit">Save Profile</button>
  </form>
  
  <div class="profile-section">
    <h2 class="section-title">Your Portfolio</h2>
    <a href="/projects/new" class="btn submit">Add New Project</a>
    
    {% if projects %}
      <div class="project-list">
        {% for project in projects %}
          <div class="project-card">
            <h3>{{ project.title }}</h3>
            <p>{{ project.short_description }}</p>
            <div class="project-actions">
              <a href="/projects/{{ project.id }}/edit" class="btn secondary">Edit</a>
              <a href="/projects/{{ project.id }}/delete" class="btn danger">Delete</a>
            </div>
          </div>
        {% endfor %}
      </div>
    {% else %}
      <p>You haven't added any projects yet.</p>
    {% endif %}
  </div>
  
  <div class="profile-section">
    <h2 class="section-title">Your Documents</h2>
    <div class="image-carousel">
      <button class="carousel-button" id="prev-button">&lt;</button>
      <div class="carousel-images" id="carousel-container">
        <div class="carousel-item">
          {% if profile.resume %}
          <img src="{{ profile.resume.url|default:'#' }}" alt="Resume" class="carousel-image">
          {% endif %}
          <div class="carousel-label">Resume</div>
        </div>
        <div class="carousel-item">
          {% if profile.certificate %}
          <img src="{{ profile.certificate.url|default:'#' }}" alt="Certificate" class="carousel-image">
          {% endif %}
          <div class="carousel-label">Certificate</div>
        </div>
        {% if profile.featured_project and profile.featured_project.image %}
        <div class="carousel-item">
          {% if profile.featured_project.image %}
          <img src="{{ profile.featured_project.image.url }}" alt="Featured Project" class="carousel-image">
          {% endif %}
          <div class="carousel-label">Featured Project</div>
        </div>
        {% endif %}
      </div>
      <button class="carousel-button" id="next-button">&gt;</button>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const container = document.getElementById('carousel-container');
    const prevButton = document.getElementById('prev-button');
    const nextButton = document.getElementById('next-button');
    const itemWidth = 220; // Width of item + gap
    
    prevButton.addEventListener('click', function() {
      container.scrollLeft -= itemWidth;
    });
    
    nextButton.addEventListener('click', function() {
      container.scrollLeft += itemWidth;
    });
    
    // Preview uploaded images before form submission
    document.getElementById('profile-picture-upload').addEventListener('change', function(e) {
      if (e.target.files && e.target.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const container = document.querySelector('.profile-image-container');
          let img = container.querySelector('.profile-image');
          
          if (!img) {
            container.querySelector('.profile-image-placeholder').remove();
            img = document.createElement('img');
            img.classList.add('profile-image');
            img.alt = 'Profile Picture';
            container.prepend(img);
          }
          
          img.src = e.target.result;
        }
        reader.readAsDataURL(e.target.files[0]);
      }
    });
    
    document.getElementById('certificate-upload').addEventListener('change', function(e) {
      if (e.target.files && e.target.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const container = document.querySelector('.certificate-container');
          let img = container.querySelector('.certificate-image');
          
          if (!img) {
            container.querySelector('.certificate-placeholder').remove();
            img = document.createElement('img');
            img.classList.add('certificate-image');
            img.alt = 'Certificate';
            container.appendChild(img);
          }
          
          img.src = e.target.result;
        }
        reader.readAsDataURL(e.target.files[0]);
      }
    });
  });
</script>
{% endblock %}