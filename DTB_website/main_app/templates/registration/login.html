{% extends 'base.html' %}
{% load static %}

{% block head %}
<style>
  .auth-container {
    max-width: 500px;
    margin: 40px auto;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background-color: white;
  }
  
  .auth-header {
    text-align: center;
    margin-bottom: 30px;
    color: black;
  }
  
  .auth-header h1 {
    font-size: var(--font-xl);
  
  }

  form {
    color: black;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    font-size: var(--font-reg);
  }
  
  .form-control {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: var(--font-reg);
  }
  
  .auth-btn {
    width: 100%;
    padding: 12px;
    background-color: #b4650e;
    color: rgb(255, 255, 255);
    border: none;
    border-radius: 4px;
    font-size: var(--font-reg);
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s;
  }
  
  .auth-btn:hover {
    background-color: #b84706;
  }
  
  .auth-footer {
    text-align: center;
    margin-top: 20px;
    font-size: var(--font-reg);
  }
  
  .auth-footer a {
    color: black;
    text-decoration: none;
  }
  
  .auth-footer a:hover {
    text-decoration: underline;
  }
  
  .error-message {
    color: #dc3545;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f8d7da;
    border-radius: 4px;
  }
  
  .password-toggle {
    color: #666;
    transition: color 0.2s;
  }
  
  .password-toggle:hover {
    color: var(--nav-bg);
  }
  
  .password-toggle:focus {
    outline: none;
  }
</style>
{% endblock %}

{% block content %}
<div class="auth-container">
  <div class="auth-header">
    <h1>Log In</h1>
    <p>Welcome back! Please log in to access your account.</p>
  </div>
  
  {% if form.errors %}
  <div class="error-message">
    Your username and password didn't match. Please try again.
  </div>
  {% endif %}
  
  <form method="post" action="{% url 'login' %}">
    {% csrf_token %}
    
    <div class="form-group">
      <label for="id_username">Username</label>
      <input type="text" name="username" id="id_username" class="form-control" required>
    </div>
    
    <div class="form-group">
      <label for="id_password">Password</label>
      <div style="position: relative;">
        <input type="password" name="password" id="id_password" class="form-control" required>
        <button type="button" class="password-toggle" onclick="togglePasswordVisibility('id_password')" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; cursor: pointer;">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="eye-icon">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="eye-off-icon" style="display: none;">
            <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
            <line x1="1" y1="1" x2="23" y2="23"></line>
          </svg>
        </button>
      </div>
    </div>
    
    <button type="submit" class="auth-btn">Log In</button>
    
    <input type="hidden" name="next" value="{{ next }}">
  </form>
  
  <div class="auth-footer">
    <p>Don't have an account? <a href="{% url 'signup' %}">Sign up</a></p>
    <p><a href="{% url 'password_reset' %}">Forgot your password?</a></p>
  </div>
</div>

<script>
  function togglePasswordVisibility(inputId) {
    const passwordInput = document.getElementById(inputId);
    const button = passwordInput.nextElementSibling;
    const eyeIcon = button.querySelector('.eye-icon');
    const eyeOffIcon = button.querySelector('.eye-off-icon');
    
    if (passwordInput.type === 'password') {
      passwordInput.type = 'text';
      eyeIcon.style.display = 'none';
      eyeOffIcon.style.display = 'block';
    } else {
      passwordInput.type = 'password';
      eyeIcon.style.display = 'block';
      eyeOffIcon.style.display = 'none';
    }
  }
</script>
{% endblock %}
