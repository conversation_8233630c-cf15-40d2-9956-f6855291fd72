{% extends 'base.html' %}
{% load static %}

{% block head %}
<style>
  /* (You can reuse the same CSS from profile.html for display) */
  .profile-container { max-width: 900px; margin: 0 auto; padding: 20px; }
  .profile-header { display: flex; align-items: center; margin-bottom: 30px; gap: 30px; }
  .profile-image-container { position: relative; width: 200px; height: 200px; }
  .profile-image { width: 100%; height: 100%; object-fit: cover; border-radius: 50%; border: 3px solid var(--nav-bg);}
  .profile-image-placeholder { width: 100%; height: 100%; border-radius: 50%; background-color: #ccc; display: flex; align-items: center; justify-content: center; font-size: var(--font-l); color: #666;}
  .profile-info { flex: 1;}
  .profile-title { font-size: var(--font-xl); margin-bottom: 10px;}
  .profile-bio { margin-bottom: 20px;}
  .profile-section { margin-bottom: 30px;}
  .section-title { font-size: var(--font-l); margin-bottom: 15px; border-bottom: 1px solid #ddd; padding-bottom: 5px;}
  .certificate-container { width: 150px; height: 150px; border: 1px solid #ddd; border-radius: var(--card-border-radius); display: flex; align-items: center; justify-content: center; margin-bottom: 10px; overflow: hidden;}
  .certificate-image { max-width: 100%; max-height: 100%; }
  .certificate-placeholder { color: #666; font-size: var(--font-reg);}
  .project-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;}
  .project-card { border: 1px solid #ddd; border-radius: var(--card-border-radius); padding: 15px;}
  .project-card h3 { margin-top: 0; font-size: var(--font-reg);}
  .edit-profile-btn { margin-top: 20px; background: #d35208; color: white; padding: 0.8em 2em; border-radius: 2em; font-weight: bold; border: none; text-decoration: none; font-size: 1em; transition: background 0.2s;}
  .edit-profile-btn:hover { background: #a03a06; color: #fff; }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
  <h1>{{ profile.name|default:profile.user.username }}'s Profile</h1>
  
  <div class="profile-header">
    <div class="profile-image-container">
      {% if profile.profile_picture %}
        <img src="{{ profile.profile_picture.url }}" alt="Profile Picture" class="profile-image">
      {% else %}
        <div class="profile-image-placeholder">
          <span>No Photo</span>
        </div>
      {% endif %}
    </div>
    
    <div class="profile-info">
      <div class="profile-title">{{ profile.title }}</div>
      <div class="profile-bio">{{ profile.bio }}</div>
    </div>
  </div>
  
  <div class="profile-section">
    <h2 class="section-title">Featured Project</h2>
    {% if profile.featured_project %}
      <div class="project-card">
        <h3>{{ profile.featured_project.title }}</h3>
        <p>{{ profile.featured_project.short_description }}</p>
        {% if profile.featured_project.image %}
          <img src="{{ profile.featured_project.image.url }}" alt="{{ profile.featured_project.title }}" style="width:100%;max-width:200px;border-radius:8px;margin-top:10px;">
        {% endif %}
      </div>
    {% else %}
      <p>No featured project selected.</p>
    {% endif %}
  </div>
  
  <div class="profile-section">
    <h2 class="section-title">Certificate/Credentials</h2>
    <div class="certificate-container">
      {% if profile.certificate %}
        <img src="{{ profile.certificate.url }}" alt="Certificate" class="certificate-image">
      {% else %}
        <div class="certificate-placeholder">
          <span>No certificate uploaded</span>
        </div>
      {% endif %}
    </div>
  </div>
  
  <div class="profile-section">
    <h2 class="section-title">Portfolio</h2>
    {% if projects %}
      <div class="project-list">
        {% for project in projects %}
          <div class="project-card">
            <h3>{{ project.title }}</h3>
            <p>{{ project.short_description }}</p>
            {% if project.image %}
              <img src="{{ project.image.url }}" alt="{{ project.title }}" style="width:100%;max-width:200px;border-radius:8px;margin-top:10px;">
            {% endif %}
          </div>
        {% endfor %}
      </div>
    {% else %}
      <p>No projects added yet.</p>
    {% endif %}
  </div>
  
  {% if user.is_authenticated and user == profile.user %}
    <a href="{% url 'profile' %}" class="edit-profile-btn">Edit Profile</a>
  {% endif %}
</div>
{% endblock %}
